import Slider from "@react-native-community/slider";
import React from "react";
import { Dimensions, View } from "react-native";
import { useProgress } from "react-native-track-player";
import { SetProgressSong } from "../../MusicPlayerFunctions";
import { SmallText } from "../Global/SmallText";
import { useThemeContext } from "../../Context/ThemeContext";
import { formatTime } from "../../Utils/TimeUtils";

export const ProgressBar = () => {
  const { theme, themeMode } = useThemeContext();
  const width = Dimensions.get("window").width;
  const { position, duration } = useProgress();

  return (
    <>
      <Slider
        onSlidingComplete={(progress) => {
          SetProgressSong(progress);
        }}
        style={{ width: width * 0.95, height: 40 }}
        minimumValue={0}
        maximumValue={duration || 1}
        value={position >= duration ? 0 : position}
        minimumTrackTintColor={theme.colors.primary}
        maximumTrackTintColor={themeMode === 'light' ? '#E0E0E0' : 'rgba(44,44,44,1)'}
        thumbTintColor={theme.colors.primary}
      />
      <View style={{ flexDirection: "row", justifyContent: "space-between", width: "90%" }}>
        <SmallText text={position >= duration ? "0:00" : formatTime(position)} style={{ fontSize: 15, color: theme.colors.text }} />
        <SmallText text={formatTime(duration)} style={{ fontSize: 15, color: theme.colors.text }} />
      </View>
    </>
  );
};
