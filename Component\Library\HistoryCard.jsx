import React, { useState, useContext, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  Pressable, 
  Image, 
  TouchableOpacity,
  Modal,
  ToastAndroid,
  Dimensions
} from 'react-native';
import { useTheme } from '@react-navigation/native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import FastImage from 'react-native-fast-image';
import Context from '../../Context/Context';
import { PlayOneSong, AddOneSongToPlaylist } from '../../MusicPlayerFunctions';
import { useActiveTrack, usePlaybackState } from 'react-native-track-player';
import { HistoryManager } from '../../Utils/HistoryManager';
import { StorageManager } from '../../Utils/StorageManager';
import { UnifiedDownloadService } from '../../Utils/UnifiedDownloadService';
import TrackPlayer from 'react-native-track-player';

const { width } = Dimensions.get('window');

export const HistoryCard = ({ song, index, allSongs, onRefresh }) => {
  const { colors, dark } = useTheme();
  const styles = getStyles(colors, dark);
  const { updateTrack } = useContext(Context);
  const currentPlaying = useActiveTrack();
  const playerState = usePlaybackState();
  
  const [menuVisible, setMenuVisible] = useState(false);
  const [menuPosition, setMenuPosition] = useState({ top: 0, right: 0 });
  const [isDownloaded, setIsDownloaded] = useState(song.isDownloaded || false);
  const [downloadInProgress, setDownloadInProgress] = useState(false);

  const isCurrentlyPlaying = currentPlaying?.id === song.id;
  const isPlaying = isCurrentlyPlaying && playerState?.state === 'playing';

  useEffect(() => {
    checkDownloadStatus();
    // Debug: Log song data to see what we have
    console.log(`🎵 History song data for "${song.title}":`, {
      sourceType: song.sourceType,
      isDownloaded: song.isDownloaded,
      isLocal: song.isLocal,
      url: song.url?.substring(0, 50) + '...'
    });
  }, [song.id]);

  const checkDownloadStatus = async () => {
    try {
      // Check if this is an online song that could be downloaded
      const isOnlineSong = !song.isLocal &&
                          !song.isDownloaded &&
                          song.sourceType !== 'local' &&
                          song.sourceType !== 'downloaded' &&
                          song.url &&
                          !song.url.startsWith('file://');

      if (isOnlineSong) {
        const downloaded = await StorageManager.isSongDownloaded(song.id);
        setIsDownloaded(downloaded);
      } else {
        // For local or already downloaded songs, set as downloaded
        setIsDownloaded(song.isDownloaded || song.sourceType === 'downloaded');
      }
    } catch (error) {
      console.error('Error checking download status:', error);
      setIsDownloaded(false);
    }
  };

  const handlePlay = async () => {
    try {
      // Create track object based on source type
      let trackData;

      if (song.sourceType === 'downloaded' || song.isDownloaded) {
        // For downloaded songs, use file path
        try {
          const songPath = await StorageManager.getSongPath(song.id, song.title);
          trackData = {
            id: song.id,
            url: `file://${songPath}`,
            title: song.title,
            artist: song.artist,
            artwork: song.artwork,
            duration: song.duration,
            isDownloaded: true,
            sourceType: 'downloaded'
          };
        } catch (pathError) {
          console.error('Error getting downloaded song path:', pathError);
          // Fallback to original URL if path fails
          trackData = {
            id: song.id,
            url: song.url,
            title: song.title,
            artist: song.artist,
            artwork: song.artwork,
            duration: song.duration,
            sourceType: 'online'
          };
        }
      } else if (song.sourceType === 'local' || song.isLocal) {
        // For local music files
        trackData = {
          id: song.id,
          url: song.url,
          title: song.title,
          artist: song.artist,
          artwork: song.artwork,
          duration: song.duration,
          isLocal: true,
          sourceType: 'local'
        };
      } else {
        // For online streaming - ensure we have a valid URL
        if (!song.url) {
          console.error('No URL available for online song:', song.title);
          ToastAndroid.show('Song URL not available', ToastAndroid.SHORT);
          return;
        }
        trackData = {
          id: song.id,
          url: song.url,
          title: song.title,
          artist: song.artist,
          artwork: song.artwork,
          duration: song.duration,
          sourceType: 'online'
        };
      }

      // Clear current queue and play the song
      await TrackPlayer.reset();
      await TrackPlayer.add(trackData);
      await TrackPlayer.play();

      updateTrack();

      // Track the play in history
      await HistoryManager.trackSongPlay(trackData);

    } catch (error) {
      console.error('Error playing song from history:', error);
      ToastAndroid.show('Error playing song', ToastAndroid.SHORT);
    }
  };

  const handleMenuPress = (event) => {
    const { pageY } = event.nativeEvent;
    setMenuPosition({
      top: pageY - 100,
      right: 20
    });
    setMenuVisible(true);
  };

  const closeMenu = () => {
    setMenuVisible(false);
  };

  const playNext = async () => {
    try {
      let trackData = {
        id: song.id,
        url: song.url,
        title: song.title,
        artist: song.artist,
        artwork: song.artwork,
        duration: song.duration,
        sourceType: song.sourceType
      };

      if (song.sourceType === 'downloaded' || song.isDownloaded) {
        const songPath = await StorageManager.getSongPath(song.id, song.title);
        trackData.url = `file://${songPath}`;
      }

      const queue = await TrackPlayer.getQueue();
      const currentIndex = await TrackPlayer.getCurrentTrack();
      
      if (currentIndex === null || queue.length === 0) {
        await TrackPlayer.add(trackData);
        await TrackPlayer.play();
      } else {
        await TrackPlayer.add(trackData, currentIndex + 1);
      }
      
      updateTrack();
      closeMenu();
      ToastAndroid.show('Song will play next', ToastAndroid.SHORT);
    } catch (error) {
      console.error('Error adding song to queue:', error);
      ToastAndroid.show('Error adding to queue', ToastAndroid.SHORT);
    }
  };

  const addToPlaylist = async () => {
    try {
      let trackData = {
        id: song.id,
        url: song.url,
        title: song.title,
        artist: song.artist,
        artwork: song.artwork,
        duration: song.duration,
        language: song.language || 'unknown'
      };

      if (song.sourceType === 'downloaded' || song.isDownloaded) {
        const songPath = await StorageManager.getSongPath(song.id, song.title);
        trackData.url = `file://${songPath}`;
      }

      // Use the existing playlist functionality
      await AddOneSongToPlaylist(trackData);
      closeMenu();
    } catch (error) {
      console.error('Error adding to playlist:', error);
      closeMenu();
      ToastAndroid.show('Error adding to playlist', ToastAndroid.SHORT);
    }
  };

  const handleDownload = async () => {
    try {
      setDownloadInProgress(true);
      closeMenu();

      const downloadData = {
        id: song.id,
        title: song.title,
        artist: song.artist,
        url: song.url,
        artwork: song.artwork,
        duration: song.duration,
        language: song.language || 'unknown'
      };

      await UnifiedDownloadService.downloadSong(downloadData);
      setIsDownloaded(true);
      ToastAndroid.show('Download completed', ToastAndroid.SHORT);

      if (onRefresh) {
        onRefresh();
      }
    } catch (error) {
      console.error('Error downloading song:', error);
      ToastAndroid.show('Download failed', ToastAndroid.SHORT);
    } finally {
      setDownloadInProgress(false);
    }
  };

  const getImageSource = () => {
    if (song.artwork) {
      if (typeof song.artwork === 'string') {
        return { uri: song.artwork };
      }
      return song.artwork;
    }
    return { uri: 'https://htmlcolorcodes.com/assets/images/colors/gray-color-solid-background-1920x1080.png' };
  };

  const formatPlayCount = (count) => {
    if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}k`;
    }
    return count.toString();
  };

  return (
    <View style={styles.container}>
      <Pressable style={styles.songCard} onPress={handlePlay}>
        <FastImage
          source={getImageSource()}
          style={styles.artwork}
          resizeMode={FastImage.resizeMode.cover}
        />
        
        <View style={styles.songInfo}>
          <Text style={styles.title} numberOfLines={1}>
            {song.title}
          </Text>
          <Text style={styles.artist} numberOfLines={1}>
            {song.artist}
          </Text>
          <View style={styles.statsRow}>
            <Text style={styles.playCount}>
              {formatPlayCount(song.totalPlayCount)} plays
            </Text>
            <Text style={styles.separator}>•</Text>
            <Text style={styles.listeningTime}>
              {HistoryManager.formatTime(song.totalListeningTime)}
            </Text>
          </View>
        </View>

        {isCurrentlyPlaying && (
          <View style={styles.playingIndicator}>
            <MaterialCommunityIcons
              name={isPlaying ? "volume-high" : "pause"}
              size={16}
              color={colors.primary}
            />
          </View>
        )}

        <TouchableOpacity
          style={styles.menuButton}
          onPress={handleMenuPress}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <MaterialCommunityIcons
            name="dots-vertical"
            size={20}
            color={colors.textSecondary}
          />
        </TouchableOpacity>
      </Pressable>

      {/* Menu Modal */}
      <Modal
        visible={menuVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={closeMenu}
      >
        <Pressable style={styles.modalOverlay} onPress={closeMenu}>
          <View style={[styles.menuContainer, { 
            top: menuPosition.top, 
            right: menuPosition.right,
            backgroundColor: dark ? '#1E1E1E' : '#FFFFFF'
          }]}>
            <TouchableOpacity style={styles.menuItem} onPress={playNext}>
              <MaterialCommunityIcons name="play-speed" size={20} color={colors.text} />
              <Text style={[styles.menuText, { color: colors.text }]}>Play next</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.menuItem} onPress={addToPlaylist}>
              <MaterialCommunityIcons name="playlist-plus" size={20} color={colors.text} />
              <Text style={[styles.menuText, { color: colors.text }]}>Add to playlist</Text>
            </TouchableOpacity>
            
            {/* Show download option only for online songs that aren't downloaded */}
            {(() => {
              const isOnlineSong = !song.isLocal &&
                                  !song.isDownloaded &&
                                  song.sourceType !== 'local' &&
                                  song.sourceType !== 'downloaded' &&
                                  song.url &&
                                  !song.url.startsWith('file://') &&
                                  !isDownloaded;

              return isOnlineSong && (
                <TouchableOpacity
                  style={styles.menuItem}
                  onPress={handleDownload}
                  disabled={downloadInProgress}
                >
                  <MaterialCommunityIcons
                    name={downloadInProgress ? "download-circle" : "download"}
                    size={20}
                    color={downloadInProgress ? colors.textSecondary : colors.text}
                  />
                  <Text style={[styles.menuText, {
                    color: downloadInProgress ? colors.textSecondary : colors.text
                  }]}>
                    {downloadInProgress ? 'Downloading...' : 'Download'}
                  </Text>
                </TouchableOpacity>
              );
            })()}
          </View>
        </Pressable>
      </Modal>
    </View>
  );
};

const getStyles = (colors, dark) => StyleSheet.create({
  container: {
    marginHorizontal: 16,
    marginVertical: 2,
  },
  songCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    backgroundColor: dark ? '#1A1A1A' : '#FFFFFF',
    borderRadius: 6,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  artwork: {
    width: 50,
    height: 50,
    borderRadius: 4,
  },
  songInfo: {
    flex: 1,
    marginLeft: 10,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 2,
  },
  artist: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: 4,
  },
  statsRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  playCount: {
    fontSize: 12,
    color: colors.textSecondary,
  },
  separator: {
    fontSize: 12,
    color: colors.textSecondary,
    marginHorizontal: 6,
  },
  listeningTime: {
    fontSize: 12,
    color: colors.textSecondary,
  },
  playingIndicator: {
    marginRight: 8,
  },
  menuButton: {
    padding: 4,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  menuContainer: {
    position: 'absolute',
    borderRadius: 8,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    minWidth: 150,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  menuText: {
    marginLeft: 12,
    fontSize: 14,
  },
});
