// Simple test to verify TrackingManager works
import TrackingManager from './TrackingManager';

export const testTrackingManager = async () => {
  try {
    console.log('Testing TrackingManager...');
    
    // Test basic functionality
    const testTrack = {
      id: 'test123',
      title: 'Test Song',
      artist: 'Test Artist',
      artwork: 'test.jpg',
      url: 'test.mp3',
      sourceType: 'online',
      isDownloaded: false,
      isLocal: false
    };
    
    // Test start tracking
    const result = await TrackingManager.startTracking(testTrack);
    console.log('Start tracking result:', result);
    
    // Test get status
    const status = TrackingManager.getTrackingStatus();
    console.log('Tracking status:', status);
    
    // Test update time
    TrackingManager.updateTime(2);
    console.log('Updated time by 2 seconds');
    
    // Test force save
    await TrackingManager.forceSave();
    console.log('Force save completed');
    
    // Test cleanup
    await TrackingManager.cleanup();
    console.log('Cleanup completed');
    
    console.log('TrackingManager test completed successfully!');
    return true;
    
  } catch (error) {
    console.error('TrackingManager test failed:', error);
    return false;
  }
};
