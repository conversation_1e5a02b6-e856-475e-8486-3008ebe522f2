import Context from "./Context";
import { useEffect, useState } from "react";
import { AppState } from "react-native";
import TrackPlayer, { Event, useTrackPlayerEvents } from "react-native-track-player";
import { getRecommendedSongs } from "../Api/Recommended";
import { AddSongsToQueue } from "../MusicPlayerFunctions";
import FormatArtist from "../Utils/FormatArtists";
import { Repeats } from "../Utils/Repeats";
import { SetQueueSongs } from "../LocalStorage/storeQueue";
import { EachSongMenuModal } from "../Component/Global/EachSongMenuModal";
import { CacheManager } from "../Utils/CacheManager";
// Import TrackingManager with fallback
let TrackingManager;
try {
    TrackingManager = require("../Utils/TrackingManager").default;
} catch (error) {
    console.error('Failed to import TrackingManager:', error);
    // Fallback TrackingManager
    TrackingManager = {
        startTracking: async () => { console.log('Fallback: startTracking'); return true; },
        stopTracking: async () => { console.log('Fallback: stopTracking'); },
        pauseTracking: async () => { console.log('Fallback: pauseTracking'); },
        resumeTracking: () => { console.log('Fallback: resumeTracking'); },
        updateTime: () => { console.log('Fallback: updateTime'); },
        getTrackingStatus: () => ({ isTracking: false, currentTrack: null, accumulatedTime: 0, trackingStartTime: null }),
        forceSave: async () => { console.log('Fallback: forceSave'); },
        cleanup: async () => { console.log('Fallback: cleanup'); }
    };
}


const events = [
    Event.PlaybackActiveTrackChanged,
    Event.PlaybackError,
    Event.PlaybackState,
    Event.PlaybackProgressUpdated,
];
const ContextState = (props)=>{
    const [Index, setIndex] = useState(0);
    const [QueueIndex, setQueueIndex] = useState(0);
    const [currentPlaying, setCurrentPlaying]  = useState({})
    const [Repeat, setRepeat] = useState(Repeats.NoRepeat);
    const [Visible, setVisible] = useState({
        visible:false,
    });
    const [previousScreen, setPreviousScreen] = useState(null);
    // Dedicated state for music player navigation - won't be affected by general navigation
    const [musicPreviousScreen, setMusicPreviousScreen] = useState("");
    
    // Add state to track the current playlist information
    const [currentPlaylistData, setCurrentPlaylistData] = useState(null);
    
    // Add state to track liked playlists for UI updates
    const [likedPlaylists, setLikedPlaylists] = useState([]);

    // Simplified tracking state - using TrackingManager
    const [isTrackingInitialized, setIsTrackingInitialized] = useState(false);

    const [Queue, setQueue] = useState([]);
    async function updateTrack (){
        const tracks = await TrackPlayer.getQueue();
        // await SetQueueSongs(tracks)
        console.log(tracks);
        const ids = tracks.map((e)=>e.id)
        const queuesId = Queue.map((e)=>e.id)
        if (JSON.stringify(ids) !== JSON.stringify(queuesId)){
            setQueue(tracks)
        }
    }
    
    // Function to update liked playlists state and trigger UI updates
    function updateLikedPlaylist() {
        // This is just to trigger rerenders when playlists are liked/unliked
        setLikedPlaylists(prev => [...prev]);
    }
    
    async function AddRecommendedSongs(index,id){
        const tracks = await TrackPlayer.getQueue();
        const totalTracks = tracks.length - 1
        if (index >= totalTracks - 2){
           try {
               const songs = await getRecommendedSongs(id)
               if (songs?.data?.length !== 0){
                   const ForMusicPlayer = songs.data.map((e)=> {
                       return {
                           url:e.downloadUrl[3].url,
                           title:e.name.toString().replaceAll("&quot;","\"").replaceAll("&amp;","and").replaceAll("&#039;","'").replaceAll("&trade;","™"),
                           artist:FormatArtist(e?.artists?.primary).toString().replaceAll("&quot;","\"").replaceAll("&amp;","and").replaceAll("&#039;","'").replaceAll("&trade;","™"),
                           artwork:e.image[2].url,
                           duration:e.duration,
                           id:e.id,
                           language:e.language,
                       }
                   })
                   await AddSongsToQueue(ForMusicPlayer)
               }
           } catch (e) {
               console.log(e);
           } finally {
               await updateTrack()
           }
        }
    }

    useTrackPlayerEvents(events, async (event) => {
        if (event.type === Event.PlaybackError) {
            console.warn('An error occured while playing the current track.');
        }

        if (event.type === Event.PlaybackProgressUpdated) {
            // Use optimized tracking manager - no duplicate tracking
            try {
                const status = TrackingManager.getTrackingStatus();
                if (status.isTracking && status.trackingStartTime) {
                    TrackingManager.updateTime(1); // Update with 1 second increment
                }
            } catch (error) {
                console.error('Error in progress tracking:', error);
            }
        }

        if (event.type === Event.PlaybackActiveTrackChanged) {
            setCurrentPlaying(event.track);

            // Use optimized tracking manager
            if (event.track?.id) {
                const songData = {
                    id: event.track.id,
                    title: event.track.title,
                    artist: event.track.artist,
                    artwork: event.track.artwork,
                    url: event.track.url,
                    sourceType: event.track.sourceType || (event.track.isDownloaded ? 'downloaded' : (event.track.isLocal ? 'local' : 'online')),
                    isDownloaded: event.track.isDownloaded || false,
                    isLocal: event.track.isLocal || false
                };

                // Start tracking with the new system
                try {
                    await TrackingManager.startTracking(songData);
                } catch (error) {
                    console.error('Error starting tracking:', error);
                }

                if (Repeat === Repeats.NoRepeat){
                    AddRecommendedSongs(event.index, event.track.id);
                }
            } else {
                // Stop tracking if no track
                try {
                    await TrackingManager.stopTracking();
                } catch (error) {
                    console.error('Error stopping tracking:', error);
                }
            }
        }

        if (event.type === Event.PlaybackState) {
            // Use optimized tracking manager for state changes
            try {
                if (event.state === 'paused' || event.state === 'stopped') {
                    await TrackingManager.pauseTracking();
                } else if (event.state === 'playing') {
                    TrackingManager.resumeTracking();
                }
            } catch (error) {
                console.error('Error handling playback state:', error);
            }
        }
    });
    async function InitialSetup(){
        try {
            // Clear old cache entries to prevent storage full errors
            await CacheManager.clearOldCacheEntries();

            await TrackPlayer.setupPlayer({
                android: {
                    appKilledPlaybackBehavior: 'StopPlaybackAndRemoveNotification'
                },
                autoHandleInterruptions: true,
                progressUpdateEventInterval: 1 // Update every 1 second
            })
            console.log('Player initialized successfully in Context with progress updates');
        } catch (error) {
            // Ignore the error if player is already initialized
            if (error.message && error.message.includes('player has already been initialized')) {
                console.log('Player already initialized in Context');
            } else {
                console.error('Error initializing player in Context:', error);
            }
        }

        await updateTrack()
        await getCurrentSong()
    }
    async function getCurrentSong(){
        const song = await TrackPlayer.getActiveTrack()
        setCurrentPlaying(song)
    }


    useEffect(() => {
        InitialSetup();

        // Add app state change listener
        const handleAppStateChange = async (nextAppState) => {
            if (nextAppState === 'background' || nextAppState === 'inactive') {
                try {
                    await TrackingManager.forceSave();
                } catch (error) {
                    console.error('Error force saving tracking data:', error);
                }
            }
        };

        const appStateSubscription = AppState.addEventListener('change', handleAppStateChange);

        return () => {
            // Cleanup when component unmounts
            try {
                TrackingManager.cleanup();
            } catch (error) {
                console.error('Error cleaning up tracking manager:', error);
            }
            appStateSubscription?.remove();
        };
    }, []);
    return <Context.Provider value={{
        currentPlaying,  
        Repeat, 
        setRepeat, 
        updateTrack, 
        Index, 
        setIndex, 
        QueueIndex, 
        setQueueIndex, 
        setVisible, 
        Queue, 
        previousScreen, 
        setPreviousScreen,
        musicPreviousScreen,
        setMusicPreviousScreen,
        currentPlaylistData,
        setCurrentPlaylistData,
        updateLikedPlaylist,
        likedPlaylists
    }}>
        {props.children}
         <EachSongMenuModal setVisible={setVisible} Visible={Visible}/>
    </Context.Provider>
}

export default  ContextState
