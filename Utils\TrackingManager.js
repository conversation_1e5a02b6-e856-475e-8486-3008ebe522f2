import { HistoryManager } from './HistoryManager';

/**
 * Centralized tracking manager to prevent duplicate tracking and optimize performance
 */
class TrackingManager {
  constructor() {
    this.currentTrack = null;
    this.trackingStartTime = null;
    this.accumulatedTime = 0;
    this.isTracking = false;
    this.saveInterval = null;
    this.pendingTimeUpdates = [];
    
    // Configuration
    this.SAVE_INTERVAL = 5000; // Save every 5 seconds
    this.MIN_TRACKING_TIME = 1; // Minimum 1 second to track
    this.MAX_BATCH_SIZE = 10; // Maximum batch size for time updates
  }

  /**
   * Start tracking a new song
   */
  async startTracking(trackData) {
    try {
      // Stop any existing tracking
      await this.stopTracking();

      if (!trackData?.id || !trackData?.title) {
        console.warn('Invalid track data for tracking:', trackData);
        return false;
      }

      // Track the song play
      const success = await HistoryManager.trackSongPlay(trackData);
      if (!success) {
        console.error('Failed to track song play');
        return false;
      }

      // Initialize tracking state
      this.currentTrack = trackData;
      this.trackingStartTime = Date.now();
      this.accumulatedTime = 0;
      this.isTracking = true;

      // Start the save interval
      this.startSaveInterval();

      console.log(`🎵 Started tracking: "${trackData.title}"`);
      return true;

    } catch (error) {
      console.error('Error starting tracking:', error);
      return false;
    }
  }

  /**
   * Update tracking time (called during playback)
   */
  updateTime(deltaSeconds) {
    if (!this.isTracking || !this.currentTrack || deltaSeconds <= 0) {
      return;
    }

    // Only track reasonable time differences
    if (deltaSeconds > 0.5 && deltaSeconds <= 5) {
      this.accumulatedTime += deltaSeconds;
    }
  }

  /**
   * Pause tracking (save current progress)
   */
  async pauseTracking() {
    if (!this.isTracking || !this.currentTrack) {
      return;
    }

    try {
      // Save any accumulated time
      if (this.accumulatedTime >= this.MIN_TRACKING_TIME) {
        await HistoryManager.trackListeningTime(
          this.currentTrack.id, 
          Math.floor(this.accumulatedTime)
        );
        this.accumulatedTime = 0;
      }

      this.isTracking = false;
      this.stopSaveInterval();

      console.log(`⏸️ Paused tracking: "${this.currentTrack.title}"`);

    } catch (error) {
      console.error('Error pausing tracking:', error);
    }
  }

  /**
   * Resume tracking
   */
  resumeTracking() {
    if (!this.currentTrack) {
      return;
    }

    this.isTracking = true;
    this.trackingStartTime = Date.now();
    this.startSaveInterval();

    console.log(`▶️ Resumed tracking: "${this.currentTrack.title}"`);
  }

  /**
   * Stop tracking completely
   */
  async stopTracking() {
    if (!this.currentTrack) {
      return;
    }

    try {
      // Save any remaining accumulated time
      if (this.accumulatedTime >= this.MIN_TRACKING_TIME) {
        await HistoryManager.trackListeningTime(
          this.currentTrack.id, 
          Math.floor(this.accumulatedTime)
        );
      }

      // Process any pending batch updates
      await this.processPendingUpdates();

      // Reset state
      this.currentTrack = null;
      this.trackingStartTime = null;
      this.accumulatedTime = 0;
      this.isTracking = false;
      this.stopSaveInterval();

      console.log('🛑 Stopped tracking');

    } catch (error) {
      console.error('Error stopping tracking:', error);
    }
  }

  /**
   * Start the periodic save interval
   */
  startSaveInterval() {
    if (this.saveInterval) {
      clearInterval(this.saveInterval);
    }

    this.saveInterval = setInterval(async () => {
      await this.saveAccumulatedTime();
    }, this.SAVE_INTERVAL);
  }

  /**
   * Stop the save interval
   */
  stopSaveInterval() {
    if (this.saveInterval) {
      clearInterval(this.saveInterval);
      this.saveInterval = null;
    }
  }

  /**
   * Save accumulated time
   */
  async saveAccumulatedTime() {
    if (!this.isTracking || !this.currentTrack || this.accumulatedTime < this.MIN_TRACKING_TIME) {
      return;
    }

    try {
      const timeToSave = Math.floor(this.accumulatedTime);
      await HistoryManager.trackListeningTime(this.currentTrack.id, timeToSave);
      
      console.log(`💾 Auto-saved ${timeToSave}s for "${this.currentTrack.title}"`);
      this.accumulatedTime = 0;

    } catch (error) {
      console.error('Error auto-saving time:', error);
    }
  }

  /**
   * Process pending batch updates
   */
  async processPendingUpdates() {
    if (this.pendingTimeUpdates.length === 0) {
      return;
    }

    try {
      await HistoryManager.batchTrackListeningTime(this.pendingTimeUpdates);
      this.pendingTimeUpdates = [];
    } catch (error) {
      console.error('Error processing pending updates:', error);
    }
  }

  /**
   * Get current tracking status
   */
  getTrackingStatus() {
    return {
      isTracking: this.isTracking,
      currentTrack: this.currentTrack,
      accumulatedTime: this.accumulatedTime,
      trackingStartTime: this.trackingStartTime
    };
  }

  /**
   * Force save current progress
   */
  async forceSave() {
    await this.saveAccumulatedTime();
    await this.processPendingUpdates();
  }

  /**
   * Cleanup - call when app is closing or component unmounting
   */
  async cleanup() {
    await this.stopTracking();
    this.stopSaveInterval();
  }
}

// Create singleton instance
const trackingManager = new TrackingManager();

export default trackingManager;
export { TrackingManager };
