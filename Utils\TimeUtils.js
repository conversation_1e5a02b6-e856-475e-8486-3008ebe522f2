/**
 * Utility functions for consistent time formatting and calculations
 */

/**
 * Format seconds to mm:ss format
 * @param {number} seconds - Time in seconds
 * @returns {string} Formatted time string
 */
export const formatTime = (seconds) => {
  if (isNaN(seconds) || seconds < 0) return "0:00";
  
  const time = Math.floor(seconds);
  const minutes = Math.floor(time / 60);
  const remainingSeconds = time % 60;
  
  return `${minutes}:${remainingSeconds < 10 ? "0" : ""}${remainingSeconds}`;
};

/**
 * Format seconds to human readable format (e.g., "2h 30m", "45m", "30s")
 * @param {number} seconds - Time in seconds
 * @returns {string} Human readable time string
 */
export const formatTimeHuman = (seconds) => {
  if (isNaN(seconds) || seconds < 0) return "0s";
  
  const time = Math.floor(seconds);
  
  if (time < 60) {
    return `${time}s`;
  } else if (time < 3600) {
    const minutes = Math.floor(time / 60);
    return `${minutes}m`;
  } else {
    const hours = Math.floor(time / 3600);
    const minutes = Math.floor((time % 3600) / 60);
    return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`;
  }
};

/**
 * Format seconds to compact format for charts (e.g., "2.5h", "45m", "30s")
 * @param {number} seconds - Time in seconds
 * @returns {string} Compact time string
 */
export const formatTimeCompact = (seconds) => {
  if (isNaN(seconds) || seconds < 0) return "0s";
  
  const time = Math.floor(seconds);
  
  if (time < 60) {
    return `${time}s`;
  } else if (time < 3600) {
    const minutes = Math.floor(time / 60);
    return `${minutes}m`;
  } else {
    const hours = time / 3600;
    return `${hours.toFixed(1)}h`;
  }
};

/**
 * Convert minutes to seconds
 * @param {number} minutes - Time in minutes
 * @returns {number} Time in seconds
 */
export const minutesToSeconds = (minutes) => {
  return Math.floor(minutes * 60);
};

/**
 * Convert seconds to minutes
 * @param {number} seconds - Time in seconds
 * @returns {number} Time in minutes
 */
export const secondsToMinutes = (seconds) => {
  return Math.floor(seconds / 60);
};

/**
 * Convert hours to seconds
 * @param {number} hours - Time in hours
 * @returns {number} Time in seconds
 */
export const hoursToSeconds = (hours) => {
  return Math.floor(hours * 3600);
};

/**
 * Convert seconds to hours
 * @param {number} seconds - Time in seconds
 * @returns {number} Time in hours
 */
export const secondsToHours = (seconds) => {
  return seconds / 3600;
};

/**
 * Calculate percentage of time played
 * @param {number} currentTime - Current playback time in seconds
 * @param {number} totalTime - Total duration in seconds
 * @returns {number} Percentage (0-100)
 */
export const calculateProgress = (currentTime, totalTime) => {
  if (!totalTime || totalTime <= 0 || !currentTime || currentTime < 0) {
    return 0;
  }
  
  const progress = (currentTime / totalTime) * 100;
  return Math.min(100, Math.max(0, progress));
};

/**
 * Validate time value
 * @param {number} time - Time value to validate
 * @returns {boolean} True if valid
 */
export const isValidTime = (time) => {
  return typeof time === 'number' && !isNaN(time) && time >= 0;
};

/**
 * Clamp time value between min and max
 * @param {number} time - Time value to clamp
 * @param {number} min - Minimum value (default: 0)
 * @param {number} max - Maximum value (default: Infinity)
 * @returns {number} Clamped time value
 */
export const clampTime = (time, min = 0, max = Infinity) => {
  if (!isValidTime(time)) return min;
  return Math.min(max, Math.max(min, time));
};

/**
 * Parse time string (mm:ss) to seconds
 * @param {string} timeString - Time string in mm:ss format
 * @returns {number} Time in seconds
 */
export const parseTimeString = (timeString) => {
  if (typeof timeString !== 'string') return 0;
  
  const parts = timeString.split(':');
  if (parts.length !== 2) return 0;
  
  const minutes = parseInt(parts[0], 10);
  const seconds = parseInt(parts[1], 10);
  
  if (isNaN(minutes) || isNaN(seconds)) return 0;
  
  return minutes * 60 + seconds;
};

/**
 * Get time difference in seconds
 * @param {number} startTime - Start timestamp
 * @param {number} endTime - End timestamp (default: current time)
 * @returns {number} Difference in seconds
 */
export const getTimeDifference = (startTime, endTime = Date.now()) => {
  if (!startTime || !endTime) return 0;
  return Math.max(0, (endTime - startTime) / 1000);
};

/**
 * Check if time difference is reasonable for tracking
 * @param {number} timeDiff - Time difference in seconds
 * @returns {boolean} True if reasonable
 */
export const isReasonableTimeDiff = (timeDiff) => {
  return timeDiff >= 0.5 && timeDiff <= 5; // Between 0.5 and 5 seconds
};

/**
 * Round time to nearest second
 * @param {number} time - Time in seconds
 * @returns {number} Rounded time
 */
export const roundTime = (time) => {
  return Math.round(time);
};

/**
 * Floor time to whole seconds
 * @param {number} time - Time in seconds
 * @returns {number} Floored time
 */
export const floorTime = (time) => {
  return Math.floor(time);
};

/**
 * Format duration for display in lists
 * @param {number} seconds - Duration in seconds
 * @returns {string} Formatted duration
 */
export const formatDuration = (seconds) => {
  if (!isValidTime(seconds)) return "--:--";
  return formatTime(seconds);
};

/**
 * Format listening time for history display
 * @param {number} seconds - Listening time in seconds
 * @returns {string} Formatted listening time
 */
export const formatListeningTime = (seconds) => {
  if (!isValidTime(seconds)) return "0s";
  return formatTimeHuman(seconds);
};

/**
 * Format chart values (minutes)
 * @param {number} minutes - Time in minutes
 * @returns {string} Formatted chart value
 */
export const formatChartValue = (minutes) => {
  if (!isValidTime(minutes)) return "0m";
  return `${Math.round(minutes)}m`;
};

export default {
  formatTime,
  formatTimeHuman,
  formatTimeCompact,
  minutesToSeconds,
  secondsToMinutes,
  hoursToSeconds,
  secondsToHours,
  calculateProgress,
  isValidTime,
  clampTime,
  parseTimeString,
  getTimeDifference,
  isReasonableTimeDiff,
  roundTime,
  floorTime,
  formatDuration,
  formatListeningTime,
  formatChartValue
};
